# CMakeList.txt for spectre_cpp
cmake_minimum_required(VERSION 3.15)
project(spectre_cpp CXX)

# --- C++ Standard ---
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# --- LibTorch Dependency ---
# ???????: ???????LibTorch??????
# ??????????????????? CMAKE_PREFIX_PATH ?????????
# ????????????????????????????????
set(CMAKE_PREFIX_PATH "E:/BaseLibrary/libtorch")
find_package(Torch REQUIRED)
if(NOT Torch_FOUND)
    message(FATAL_ERROR "LibTorch not found. Please set CMAKE_PREFIX_PATH.")
endif()
message(STATUS "Found LibTorch: ${TORCH_LIBRARIES}")

# --- Include Directories ---
# ???????????????????????????
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# --- Add Executable ---
# ???????????????
add_executable(spectre_main
    src/main.cpp
    src/Factor.cpp
    src/Engine.cpp
    src/Rolling.cpp
    src/Parallel.cpp
    src/RollingParallel.cpp
    src/ParallelAll.cpp
)

# --- Link Libraries ---
# ???????????????Torch??
target_link_libraries(spectre_main PRIVATE ${TORCH_LIBRARIES})

# --- Final Setup for MSVC (Windows) ---
if(MSVC)
    # ????????????????Torch??DLL
    file(GLOB TORCH_DLLS "${TORCH_INSTALL_PREFIX}/lib/*.dll")
    add_custom_command(TARGET spectre_main POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        ${TORCH_DLLS}
        $<TARGET_FILE_DIR:spectre_main>)
endif()

message(STATUS "Project setup complete. You can now build the 'spectre_main' target.")
